/**
 * 渲染服务主服务
 */
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { v4 as uuidv4 } from 'uuid';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);
  private readonly instanceId = uuidv4();

  constructor(
    private readonly configService: ConfigService,
    @Inject('SERVICE_REGISTRY') private readonly serviceRegistry: ClientProxy,
  ) {}

  async onModuleInit() {
    await this.registerService();
    this.startHeartbeat();
  }

  getInfo() {
    return {
      name: '渲染服务',
      version: '1.0.0',
      description: 'DL（Digital Learning）引擎渲染服务',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      instanceId: this.instanceId,
    };
  }

  private async registerService() {
    try {
      const host = this.configService.get<string>('RENDER_SERVICE_HOST', 'localhost');
      const port = this.configService.get<number>('RENDER_SERVICE_PORT', 3004);
      const httpPort = this.configService.get<number>('RENDER_SERVICE_HTTP_PORT', 4004);

      await firstValueFrom(
        this.serviceRegistry.send(
          { cmd: 'register' },
          {
            name: 'render-service',
            description: 'DL（Digital Learning）引擎渲染服务',
            instanceId: this.instanceId,
            host,
            port,
            httpPort,
            metadata: {
              version: '1.0.0',
              environment: this.configService.get<string>('NODE_ENV', 'development'),
            },
          },
        ),
      );

      this.logger.log('渲染服务已注册到服务注册中心');
    } catch (error) {
      this.logger.error('注册服务失败', error);
    }
  }

  private startHeartbeat() {
    setInterval(async () => {
      try {
        await firstValueFrom(
          this.serviceRegistry.send(
            { cmd: 'heartbeat' },
            {
              name: 'render-service',
              instanceId: this.instanceId,
              status: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
              },
            },
          ),
        );
        this.logger.debug('发送心跳成功');
      } catch (error) {
        this.logger.error('发送心跳失败', error);
      }
    }, 30000); // 每30秒发送一次心跳
  }
}
